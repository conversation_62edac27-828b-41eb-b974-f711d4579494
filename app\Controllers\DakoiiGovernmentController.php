<?php

namespace App\Controllers;

use App\Models\CountryModel;
use App\Models\ProvinceModel;
use App\Models\DistrictModel;
use App\Models\LlgModel;

class DakoiiGovernmentController extends BaseController
{
    // Government Overview
    public function index()
    {
        return view('dakoii/dakoii_government_overview');
    }

    // Countries
    public function listCountries()
    {
        $model = new CountryModel();
        $countries = $model->findAll();
        return view('dakoii/dakoii_government_countries', ['countries' => $countries]);
    }
    public function showCountry($id)
    {
        $model = new CountryModel();
        $country = $model->find($id);
        if (!$country) throw \CodeIgniter\Exceptions\PageNotFoundException::forPageNotFound('Country not found');
        return view('dakoii/dakoii_government_country_profile', ['country' => $country]);
    }
    /**
     * Show the create country form (GET)
     */
    public function createCountryForm()
    {
        helper(['form']);

        // Debug logging
        log_message('debug', 'Accessing create country form');
        log_message('debug', 'Session data: ' . print_r(session()->get(), true));

        return view('dakoii/dakoii_government_country_create');
    }

    /**
     * Store a new country (POST)
     */
    public function storeCountry()
    {
        helper(['form']);

        // Debug logging
        log_message('debug', 'Store country method called');
        log_message('debug', 'Request method: ' . $this->request->getMethod());
        log_message('debug', 'Session data: ' . print_r(session()->get(), true));

        $model = new CountryModel();
        $data = $this->request->getPost();

        // Debug logging
        log_message('debug', 'Country store POST data: ' . print_r($data, true));

        // Check if we have any POST data
        if (empty($data)) {
            log_message('error', 'No POST data received in storeCountry method');
            return view('dakoii/dakoii_government_country_create', [
                'validation' => ['general' => 'No data received. Please try again.'],
                'old_data' => []
            ]);
        }

        // Process GPS coordinates if provided
        if (!empty($data['map_centre_gps'])) {
            $gps = explode(',', $data['map_centre_gps']);
            if (count($gps) == 2) {
                $data['map_centre_lat'] = trim($gps[0]);
                $data['map_centre_lng'] = trim($gps[1]);
            }
            unset($data['map_centre_gps']); // Remove the combined field
        }

        // Remove geojson_id as it's not in the database
        unset($data['geojson_id']);

        // Add audit fields
        $userId = session()->get('dakoii_user_id');
        log_message('debug', 'Current user ID from session: ' . $userId);

        // For debug routes, use a default user ID if not authenticated
        if (!$userId) {
            $currentUrl = current_url();
            if (strpos($currentUrl, '/debug/') !== false) {
                log_message('debug', 'Debug route detected, using default user ID');
                $userId = 1; // Default user for testing
            } else {
                log_message('error', 'No user ID in session - authentication issue');
                return redirect()->to('/dakoii')->with('error', 'Authentication required. Please log in again.');
            }
        }

        $data['created_by'] = $userId;

        // Validate uniqueness manually for create
        $existingIso2 = $model->where('iso2', $data['iso2'])->where('deleted_at', null)->first();
        $existingIso3 = $model->where('iso3', $data['iso3'])->where('deleted_at', null)->first();

        $validationErrors = [];
        if ($existingIso2) {
            $validationErrors['iso2'] = 'The ISO2 code is already taken.';
        }
        if ($existingIso3) {
            $validationErrors['iso3'] = 'The ISO3 code is already taken.';
        }

        if (!empty($validationErrors)) {
            log_message('debug', 'Validation errors: ' . print_r($validationErrors, true));
            return view('dakoii/dakoii_government_country_create', [
                'validation' => $validationErrors,
                'old_data' => $data
            ]);
        }

        // Clean data - only keep allowed fields
        $allowedData = [];
        foreach ($model->getAllowedFields() as $field) {
            if (isset($data[$field])) {
                $allowedData[$field] = $data[$field];
            }
        }

        // Debug logging
        log_message('debug', 'Country store allowed data: ' . print_r($allowedData, true));

        try {
            if ($model->insert($allowedData)) {
                $insertId = $model->getInsertID();
                log_message('debug', 'Country created successfully with ID: ' . $insertId);
                return redirect()->to('/dakoii/government/countries')->with('success', 'Country created successfully');
            } else {
                // Debug logging for errors
                $errors = $model->errors();
                log_message('error', 'Country create failed. Model errors: ' . print_r($errors, true));

                return view('dakoii/dakoii_government_country_create', [
                    'validation' => $errors,
                    'old_data' => $data
                ]);
            }
        } catch (\Exception $e) {
            log_message('error', 'Exception in storeCountry: ' . $e->getMessage());
            log_message('error', 'Exception trace: ' . $e->getTraceAsString());

            return view('dakoii/dakoii_government_country_create', [
                'validation' => ['general' => 'An error occurred: ' . $e->getMessage()],
                'old_data' => $data
            ]);
        }
    }
    /**
     * Show the edit country form (GET)
     */
    public function editCountryForm($id)
    {
        helper(['form']);
        $model = new CountryModel();
        $country = $model->find($id);

        if (!$country) {
            throw \CodeIgniter\Exceptions\PageNotFoundException::forPageNotFound('Country not found');
        }

        return view('dakoii/dakoii_government_country_edit', ['country' => $country]);
    }

    /**
     * Update a country (POST)
     */
    public function updateCountry($id)
    {
        helper(['form']);
        $model = new CountryModel();
        $country = $model->find($id);

        if (!$country) {
            throw \CodeIgniter\Exceptions\PageNotFoundException::forPageNotFound('Country not found');
        }

        $data = $this->request->getPost();

        // Process GPS coordinates if provided
        if (!empty($data['map_centre_gps'])) {
            $gps = explode(',', $data['map_centre_gps']);
            if (count($gps) == 2) {
                $data['map_centre_lat'] = trim($gps[0]);
                $data['map_centre_lng'] = trim($gps[1]);
            }
            unset($data['map_centre_gps']); // Remove the combined field
        }

        // Remove geojson_id as it's not in the database
        unset($data['geojson_id']);

        // Validate uniqueness manually for update (exclude current record)
        $existingIso2 = $model->where('iso2', $data['iso2'])->where('id !=', $id)->where('deleted_at', null)->first();
        $existingIso3 = $model->where('iso3', $data['iso3'])->where('id !=', $id)->where('deleted_at', null)->first();

        $validationErrors = [];
        if ($existingIso2) {
            $validationErrors['iso2'] = 'The ISO2 code is already taken.';
        }
        if ($existingIso3) {
            $validationErrors['iso3'] = 'The ISO3 code is already taken.';
        }

        if (!empty($validationErrors)) {
            return view('dakoii/dakoii_government_country_edit', [
                'country' => $country,
                'validation' => $validationErrors,
                'old_data' => $data
            ]);
        }

        // Add audit fields
        $data['updated_by'] = session()->get('dakoii_user_id');

        // Clean data - only keep allowed fields
        $allowedData = [];
        foreach ($model->getAllowedFields() as $field) {
            if (isset($data[$field])) {
                $allowedData[$field] = $data[$field];
            }
        }

        if ($model->update($id, $allowedData)) {
            return redirect()->to('/dakoii/government/countries/'.$id)->with('success', 'Country updated successfully');
        }

        return view('dakoii/dakoii_government_country_edit', [
            'country' => $country,
            'validation' => $model->errors(),
            'old_data' => $data
        ]);
    }
    public function deleteCountry($id)
    {
        $model = new CountryModel();

        // Set deleted_by before soft delete
        $model->update($id, ['deleted_by' => session()->get('dakoii_user_id')]);
        $model->delete($id);

        return redirect()->to('/dakoii/government/countries')->with('success', 'Country deleted successfully');
    }

    // Provinces
    public function listProvinces()
    {
        $model = new ProvinceModel();
        $provinces = $model->findAll();
        return view('dakoii/dakoii_government_provinces', ['provinces' => $provinces]);
    }
    public function showProvince($id)
    {
        $model = new ProvinceModel();
        $province = $model->find($id);
        if (!$province) throw \CodeIgniter\Exceptions\PageNotFoundException::forPageNotFound('Province not found');
        return view('dakoii/dakoii_government_province_profile', ['province' => $province]);
    }
    public function createProvince()
    {
        helper(['form']);
        $model = new ProvinceModel();
        if ($this->request->getMethod() === 'post') {
            $data = $this->request->getPost();

            // Add audit fields
            $data['created_by'] = session()->get('dakoii_user_id');

            if ($model->insert($data)) {
                return redirect()->to('/dakoii/government/provinces')->with('success', 'Province created successfully');
            }
            return view('dakoii/dakoii_government_province_create', ['validation' => $model->errors()]);
        }
        return view('dakoii/dakoii_government_province_create');
    }
    public function updateProvince($id)
    {
        helper(['form']);
        $model = new ProvinceModel();
        $province = $model->find($id);
        if (!$province) throw \CodeIgniter\Exceptions\PageNotFoundException::forPageNotFound('Province not found');
        if ($this->request->getMethod() === 'post') {
            $data = $this->request->getPost();

            // Add audit fields
            $data['updated_by'] = session()->get('dakoii_user_id');

            if ($model->update($id, $data)) {
                return redirect()->to('/dakoii/government/provinces/'.$id)->with('success', 'Province updated successfully');
            }
            return view('dakoii/dakoii_government_province_edit', ['province' => $province, 'validation' => $model->errors()]);
        }
        return view('dakoii/dakoii_government_province_edit', ['province' => $province]);
    }
    public function deleteProvince($id)
    {
        $model = new ProvinceModel();

        // Set deleted_by before soft delete
        $model->update($id, ['deleted_by' => session()->get('dakoii_user_id')]);
        $model->delete($id);

        return redirect()->to('/dakoii/government/provinces')->with('success', 'Province deleted successfully');
    }

    // Districts
    public function listDistricts()
    {
        $model = new DistrictModel();
        $districts = $model->findAll();
        return view('dakoii/dakoii_government_districts', ['districts' => $districts]);
    }
    public function showDistrict($id)
    {
        $model = new DistrictModel();
        $district = $model->find($id);
        if (!$district) throw \CodeIgniter\Exceptions\PageNotFoundException::forPageNotFound('District not found');
        return view('dakoii/dakoii_government_district_profile', ['district' => $district]);
    }
    public function createDistrict()
    {
        helper(['form']);
        $model = new DistrictModel();
        if ($this->request->getMethod() === 'post') {
            $data = $this->request->getPost();

            // Add audit fields
            $data['created_by'] = session()->get('dakoii_user_id');

            if ($model->insert($data)) {
                return redirect()->to('/dakoii/government/districts')->with('success', 'District created successfully');
            }
            return view('dakoii/dakoii_government_district_create', ['validation' => $model->errors()]);
        }
        return view('dakoii/dakoii_government_district_create');
    }
    public function updateDistrict($id)
    {
        helper(['form']);
        $model = new DistrictModel();
        $district = $model->find($id);
        if (!$district) throw \CodeIgniter\Exceptions\PageNotFoundException::forPageNotFound('District not found');
        if ($this->request->getMethod() === 'post') {
            $data = $this->request->getPost();

            // Add audit fields
            $data['updated_by'] = session()->get('dakoii_user_id');

            if ($model->update($id, $data)) {
                return redirect()->to('/dakoii/government/districts/'.$id)->with('success', 'District updated successfully');
            }
            return view('dakoii/dakoii_government_district_edit', ['district' => $district, 'validation' => $model->errors()]);
        }
        return view('dakoii/dakoii_government_district_edit', ['district' => $district]);
    }
    public function deleteDistrict($id)
    {
        $model = new DistrictModel();

        // Set deleted_by before soft delete
        $model->update($id, ['deleted_by' => session()->get('dakoii_user_id')]);
        $model->delete($id);

        return redirect()->to('/dakoii/government/districts')->with('success', 'District deleted successfully');
    }

    // LLGs
    public function listLlgs()
    {
        $model = new LlgModel();
        $llgs = $model->findAll();
        return view('dakoii/dakoii_government_llgs', ['llgs' => $llgs]);
    }
    public function showLlg($id)
    {
        $model = new LlgModel();
        $llg = $model->find($id);
        if (!$llg) throw \CodeIgniter\Exceptions\PageNotFoundException::forPageNotFound('LLG not found');
        return view('dakoii/dakoii_government_llg_profile', ['llg' => $llg]);
    }
    public function createLlg()
    {
        helper(['form']);
        $model = new LlgModel();
        if ($this->request->getMethod() === 'post') {
            $data = $this->request->getPost();

            // Add audit fields
            $data['created_by'] = session()->get('dakoii_user_id');

            if ($model->insert($data)) {
                return redirect()->to('/dakoii/government/llgs')->with('success', 'LLG created successfully');
            }
            return view('dakoii/dakoii_government_llg_create', ['validation' => $model->errors()]);
        }
        return view('dakoii/dakoii_government_llg_create');
    }
    public function updateLlg($id)
    {
        helper(['form']);
        $model = new LlgModel();
        $llg = $model->find($id);
        if (!$llg) throw \CodeIgniter\Exceptions\PageNotFoundException::forPageNotFound('LLG not found');
        if ($this->request->getMethod() === 'post') {
            $data = $this->request->getPost();

            // Add audit fields
            $data['updated_by'] = session()->get('dakoii_user_id');

            if ($model->update($id, $data)) {
                return redirect()->to('/dakoii/government/llgs/'.$id)->with('success', 'LLG updated successfully');
            }
            return view('dakoii/dakoii_government_llg_edit', ['llg' => $llg, 'validation' => $model->errors()]);
        }
        return view('dakoii/dakoii_government_llg_edit', ['llg' => $llg]);
    }
    public function deleteLlg($id)
    {
        $model = new LlgModel();

        // Set deleted_by before soft delete
        $model->update($id, ['deleted_by' => session()->get('dakoii_user_id')]);
        $model->delete($id);

        return redirect()->to('/dakoii/government/llgs')->with('success', 'LLG deleted successfully');
    }

    // Hierarchy Chart and Map
    public function chart()
    {
        return view('dakoii/dakoii_government_chart');
    }
    public function map()
    {
        return view('dakoii/dakoii_government_map');
    }
} 