<?php

namespace App\Controllers;

use App\Models\CountryModel;
use App\Models\ProvinceModel;
use App\Models\DistrictModel;
use App\Models\LlgModel;

class DakoiiGovernmentController extends BaseController
{
    // Government Overview
    public function index()
    {
        return view('dakoii/dakoii_government_overview');
    }

    // Countries
    public function listCountries()
    {
        $model = new CountryModel();
        $countries = $model->findAll();
        return view('dakoii/dakoii_government_countries', ['countries' => $countries]);
    }
    public function showCountry($id)
    {
        $model = new CountryModel();
        $country = $model->find($id);
        if (!$country) throw \CodeIgniter\Exceptions\PageNotFoundException::forPageNotFound('Country not found');
        return view('dakoii/dakoii_government_country_profile', ['country' => $country]);
    }
    public function createCountry()
    {
        helper(['form']);
        $model = new CountryModel();
        if ($this->request->getMethod() === 'post') {
            $data = $this->request->getPost();

            // Add audit fields
            $data['created_by'] = session()->get('dakoii_user_id');

            if ($model->insert($data)) {
                return redirect()->to('/dakoii/government/countries')->with('success', 'Country created successfully');
            }
            return view('dakoii/dakoii_government_country_create', ['validation' => $model->errors()]);
        }
        return view('dakoii/dakoii_government_country_create');
    }
    public function updateCountry($id)
    {
        helper(['form']);
        $model = new CountryModel();
        $country = $model->find($id);
        if (!$country) throw \CodeIgniter\Exceptions\PageNotFoundException::forPageNotFound('Country not found');
        if ($this->request->getMethod() === 'post') {
            $data = $this->request->getPost();

            // Add audit fields
            $data['updated_by'] = session()->get('dakoii_user_id');

            if ($model->update($id, $data)) {
                return redirect()->to('/dakoii/government/countries/'.$id)->with('success', 'Country updated successfully');
            }
            return view('dakoii/dakoii_government_country_edit', ['country' => $country, 'validation' => $model->errors()]);
        }
        return view('dakoii/dakoii_government_country_edit', ['country' => $country]);
    }
    public function deleteCountry($id)
    {
        $model = new CountryModel();

        // Set deleted_by before soft delete
        $model->update($id, ['deleted_by' => session()->get('dakoii_user_id')]);
        $model->delete($id);

        return redirect()->to('/dakoii/government/countries')->with('success', 'Country deleted successfully');
    }

    // Provinces
    public function listProvinces()
    {
        $model = new ProvinceModel();
        $provinces = $model->findAll();
        return view('dakoii/dakoii_government_provinces', ['provinces' => $provinces]);
    }
    public function showProvince($id)
    {
        $model = new ProvinceModel();
        $province = $model->find($id);
        if (!$province) throw \CodeIgniter\Exceptions\PageNotFoundException::forPageNotFound('Province not found');
        return view('dakoii/dakoii_government_province_profile', ['province' => $province]);
    }
    public function createProvince()
    {
        helper(['form']);
        $model = new ProvinceModel();
        if ($this->request->getMethod() === 'post') {
            $data = $this->request->getPost();

            // Add audit fields
            $data['created_by'] = session()->get('dakoii_user_id');

            if ($model->insert($data)) {
                return redirect()->to('/dakoii/government/provinces')->with('success', 'Province created successfully');
            }
            return view('dakoii/dakoii_government_province_create', ['validation' => $model->errors()]);
        }
        return view('dakoii/dakoii_government_province_create');
    }
    public function updateProvince($id)
    {
        helper(['form']);
        $model = new ProvinceModel();
        $province = $model->find($id);
        if (!$province) throw \CodeIgniter\Exceptions\PageNotFoundException::forPageNotFound('Province not found');
        if ($this->request->getMethod() === 'post') {
            $data = $this->request->getPost();

            // Add audit fields
            $data['updated_by'] = session()->get('dakoii_user_id');

            if ($model->update($id, $data)) {
                return redirect()->to('/dakoii/government/provinces/'.$id)->with('success', 'Province updated successfully');
            }
            return view('dakoii/dakoii_government_province_edit', ['province' => $province, 'validation' => $model->errors()]);
        }
        return view('dakoii/dakoii_government_province_edit', ['province' => $province]);
    }
    public function deleteProvince($id)
    {
        $model = new ProvinceModel();

        // Set deleted_by before soft delete
        $model->update($id, ['deleted_by' => session()->get('dakoii_user_id')]);
        $model->delete($id);

        return redirect()->to('/dakoii/government/provinces')->with('success', 'Province deleted successfully');
    }

    // Districts
    public function listDistricts()
    {
        $model = new DistrictModel();
        $districts = $model->findAll();
        return view('dakoii/dakoii_government_districts', ['districts' => $districts]);
    }
    public function showDistrict($id)
    {
        $model = new DistrictModel();
        $district = $model->find($id);
        if (!$district) throw \CodeIgniter\Exceptions\PageNotFoundException::forPageNotFound('District not found');
        return view('dakoii/dakoii_government_district_profile', ['district' => $district]);
    }
    public function createDistrict()
    {
        helper(['form']);
        $model = new DistrictModel();
        if ($this->request->getMethod() === 'post') {
            $data = $this->request->getPost();

            // Add audit fields
            $data['created_by'] = session()->get('dakoii_user_id');

            if ($model->insert($data)) {
                return redirect()->to('/dakoii/government/districts')->with('success', 'District created successfully');
            }
            return view('dakoii/dakoii_government_district_create', ['validation' => $model->errors()]);
        }
        return view('dakoii/dakoii_government_district_create');
    }
    public function updateDistrict($id)
    {
        helper(['form']);
        $model = new DistrictModel();
        $district = $model->find($id);
        if (!$district) throw \CodeIgniter\Exceptions\PageNotFoundException::forPageNotFound('District not found');
        if ($this->request->getMethod() === 'post') {
            $data = $this->request->getPost();

            // Add audit fields
            $data['updated_by'] = session()->get('dakoii_user_id');

            if ($model->update($id, $data)) {
                return redirect()->to('/dakoii/government/districts/'.$id)->with('success', 'District updated successfully');
            }
            return view('dakoii/dakoii_government_district_edit', ['district' => $district, 'validation' => $model->errors()]);
        }
        return view('dakoii/dakoii_government_district_edit', ['district' => $district]);
    }
    public function deleteDistrict($id)
    {
        $model = new DistrictModel();

        // Set deleted_by before soft delete
        $model->update($id, ['deleted_by' => session()->get('dakoii_user_id')]);
        $model->delete($id);

        return redirect()->to('/dakoii/government/districts')->with('success', 'District deleted successfully');
    }

    // LLGs
    public function listLlgs()
    {
        $model = new LlgModel();
        $llgs = $model->findAll();
        return view('dakoii/dakoii_government_llgs', ['llgs' => $llgs]);
    }
    public function showLlg($id)
    {
        $model = new LlgModel();
        $llg = $model->find($id);
        if (!$llg) throw \CodeIgniter\Exceptions\PageNotFoundException::forPageNotFound('LLG not found');
        return view('dakoii/dakoii_government_llg_profile', ['llg' => $llg]);
    }
    public function createLlg()
    {
        helper(['form']);
        $model = new LlgModel();
        if ($this->request->getMethod() === 'post') {
            $data = $this->request->getPost();

            // Add audit fields
            $data['created_by'] = session()->get('dakoii_user_id');

            if ($model->insert($data)) {
                return redirect()->to('/dakoii/government/llgs')->with('success', 'LLG created successfully');
            }
            return view('dakoii/dakoii_government_llg_create', ['validation' => $model->errors()]);
        }
        return view('dakoii/dakoii_government_llg_create');
    }
    public function updateLlg($id)
    {
        helper(['form']);
        $model = new LlgModel();
        $llg = $model->find($id);
        if (!$llg) throw \CodeIgniter\Exceptions\PageNotFoundException::forPageNotFound('LLG not found');
        if ($this->request->getMethod() === 'post') {
            $data = $this->request->getPost();

            // Add audit fields
            $data['updated_by'] = session()->get('dakoii_user_id');

            if ($model->update($id, $data)) {
                return redirect()->to('/dakoii/government/llgs/'.$id)->with('success', 'LLG updated successfully');
            }
            return view('dakoii/dakoii_government_llg_edit', ['llg' => $llg, 'validation' => $model->errors()]);
        }
        return view('dakoii/dakoii_government_llg_edit', ['llg' => $llg]);
    }
    public function deleteLlg($id)
    {
        $model = new LlgModel();

        // Set deleted_by before soft delete
        $model->update($id, ['deleted_by' => session()->get('dakoii_user_id')]);
        $model->delete($id);

        return redirect()->to('/dakoii/government/llgs')->with('success', 'LLG deleted successfully');
    }

    // Hierarchy Chart and Map
    public function chart()
    {
        return view('dakoii/dakoii_government_chart');
    }
    public function map()
    {
        return view('dakoii/dakoii_government_map');
    }
} 