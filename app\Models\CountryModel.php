<?php

namespace App\Models;

/**
 * Model for the 'countries' table.
 * Represents countries with ISO codes, name, map defaults, and audit columns.
 */
class CountryModel extends BaseModel
{
    protected $table = 'countries';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = true;
    protected $allowedFields = [
        'iso2', 'iso3', 'name',
        'map_centre_gps', 'map_zoom',
        'created_by', 'updated_by', 'deleted_by',
    ];
    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';

    /**
     * Only essential validation rules: required and uniqueness for iso2 and iso3, required for name.
     */
    protected $validationRules = [
        'iso2' => 'required|is_unique[countries.iso2]',
        'iso3' => 'required|is_unique[countries.iso3]',
        'name' => 'required',
    ];
    protected $validationMessages = [];
    protected $skipValidation = false;
    protected $cleanValidationRules = true;
    protected $allowCallbacks = true;
    protected $beforeInsert   = [];
    protected $afterInsert    = [];
    protected $beforeUpdate   = [];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = [];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];
} 