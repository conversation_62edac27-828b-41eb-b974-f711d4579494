<?php

namespace App\Models;

/**
 * Model for the 'countries' table.
 * Represents countries with ISO codes, name, map defaults, and audit columns.
 */
class CountryModel extends BaseModel
{
    protected $table = 'countries';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = true;
    protected $allowedFields = [
        'iso2', 'iso3', 'name',
        'map_centre_lat', 'map_centre_lng', 'map_zoom',
        'created_by', 'updated_by', 'deleted_by',
    ];
    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';

    /**
     * Only essential validation rules: required and uniqueness for iso2 and iso3, required for name.
     * Note: For updates, uniqueness should be handled in the controller to exclude current record.
     */
    protected $validationRules = [
        'iso2' => 'required|max_length[2]',
        'iso3' => 'required|max_length[3]',
        'name' => 'required|max_length[100]',
        'map_centre_lat' => 'permit_empty|decimal',
        'map_centre_lng' => 'permit_empty|decimal',
        'map_zoom' => 'permit_empty|integer|greater_than[0]|less_than_equal_to[20]',
    ];
    protected $validationMessages = [];
    protected $skipValidation = false;
    protected $cleanValidationRules = true;
    protected $allowCallbacks = true;
    protected $beforeInsert   = [];
    protected $afterInsert    = [];
    protected $beforeUpdate   = [];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = [];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];

    /**
     * Get the allowed fields for this model
     */
    public function getAllowedFields()
    {
        return $this->allowedFields;
    }
}