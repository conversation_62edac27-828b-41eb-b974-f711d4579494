<?php

namespace App\Models;

/**
 * Model for the 'llgs' table.
 * Represents Local-Level Governments (LLGs) with district reference, code, name, map defaults, and audit columns.
 */
class LlgModel extends BaseModel
{
    protected $table = 'llgs';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = true;
    protected $allowedFields = [
        'district_id', 'llg_code', 'name',
        'map_centre_gps', 'map_zoom',
        'created_by', 'updated_by', 'deleted_by',
    ];
    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';

    /**
     * Only essential validation rules: required for district_id, llg_code, and name.
     */
    protected $validationRules = [
        'district_id' => 'required',
        'llg_code'    => 'required',
        'name'        => 'required',
    ];
    protected $validationMessages = [];
    protected $skipValidation = false;
    protected $cleanValidationRules = true;
    protected $allowCallbacks = true;
    protected $beforeInsert   = [];
    protected $afterInsert    = [];
    protected $beforeUpdate   = [];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = [];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];
} 